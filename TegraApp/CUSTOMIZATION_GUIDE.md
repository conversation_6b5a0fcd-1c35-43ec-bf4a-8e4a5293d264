# 🎨 Guia de Customização - TEGRA

Este guia ajudará você a adaptar o app TEGRA ao design específico do Figma.

## 📋 Checklist de Customização

### 1. Cores e Tema
- [ ] Extrair paleta de cores do Figma
- [ ] Atualizar `src/styles/colors.ts`
- [ ] Verificar contraste e acessibilidade
- [ ] Testar em modo claro e escuro (se aplicável)

### 2. Tipografia
- [ ] Identificar fontes utilizadas no design
- [ ] Instalar fontes customizadas (se necessário)
- [ ] Atualizar tamanhos e pesos em `src/config/theme.ts`
- [ ] Ajustar line-heights e espaçamentos

### 3. Layout e Espaçamentos
- [ ] Medir espaçamentos no Figma
- [ ] Atualizar `theme.layout.spacing`
- [ ] Ajustar padding e margins dos componentes
- [ ] Verificar responsividade

### 4. Componentes Específicos

#### Tela de Login
- [ ] Logo/Ícone principal
- [ ] Posicionamento dos elementos
- [ ] Estilo dos inputs
- [ ] Botões (cores, bordas, sombras)
- [ ] Textos e links

#### Componentes Reutilizáveis
- [ ] Botões (Button.tsx)
- [ ] Inputs (Input.tsx)
- [ ] Cards e containers

## 🛠️ Como Customizar

### 1. Atualizando Cores

```typescript
// src/styles/colors.ts
export const colors = {
  primary: {
    600: '#SUA_COR_PRIMARIA', // Cor principal do Figma
    // ... outras variações
  },
  // ... outras cores
};
```

### 2. Adicionando Fontes Customizadas

1. **Adicione os arquivos de fonte:**
   ```
   TegraApp/
   ├── android/app/src/main/assets/fonts/
   │   ├── CustomFont-Regular.ttf
   │   └── CustomFont-Bold.ttf
   └── ios/TegraApp/Fonts/
       ├── CustomFont-Regular.ttf
       └── CustomFont-Bold.ttf
   ```

2. **Configure no iOS (Info.plist):**
   ```xml
   <key>UIAppFonts</key>
   <array>
     <string>CustomFont-Regular.ttf</string>
     <string>CustomFont-Bold.ttf</string>
   </array>
   ```

3. **Use nos estilos:**
   ```typescript
   const styles = StyleSheet.create({
     text: {
       fontFamily: 'CustomFont-Regular',
     },
   });
   ```

### 3. Customizando a Tela de Login

#### Exemplo de customização baseada no Figma:

```typescript
// src/screens/LoginScreen.tsx

// Adicione suas cores específicas
const customColors = {
  primaryBlue: '#1E40AF',    // Cor do Figma
  lightGray: '#F8F9FA',     // Background dos inputs
  darkText: '#2D3748',      // Texto principal
  // ... outras cores
};

// Atualize os estilos
const styles = StyleSheet.create({
  logoText: {
    fontSize: 32,             // Tamanho do Figma
    fontWeight: 'bold',
    color: customColors.primaryBlue,
    letterSpacing: 2,         // Espaçamento do Figma
    // fontFamily: 'SuaFonte', // Se usar fonte customizada
  },
  
  input: {
    height: 56,               // Altura do Figma
    borderWidth: 1,
    borderColor: '#E2E8F0',   // Cor da borda do Figma
    borderRadius: 8,          // Border radius do Figma
    backgroundColor: customColors.lightGray,
    // ... outros estilos
  },
  
  loginButton: {
    height: 48,               // Altura do botão no Figma
    backgroundColor: customColors.primaryBlue,
    borderRadius: 8,
    // Sombra conforme o Figma
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});
```

### 4. Adicionando Ícones e Imagens

1. **Instale react-native-vector-icons (se necessário):**
   ```bash
   npm install react-native-vector-icons
   ```

2. **Adicione imagens na pasta assets:**
   ```
   src/assets/
   ├── images/
   │   ├── logo.png
   │   ├── <EMAIL>
   │   └── <EMAIL>
   └── icons/
   ```

3. **Use nas telas:**
   ```typescript
   import { Image } from 'react-native';
   
   <Image 
     source={require('../assets/images/logo.png')} 
     style={styles.logo}
   />
   ```

### 5. Medindo Elementos do Figma

#### Ferramentas úteis:
- **Inspect no Figma:** Clique no elemento e veja as propriedades
- **Exportar assets:** Use a função de exportar do Figma
- **Copiar CSS:** Figma pode gerar CSS que você pode adaptar

#### Conversão de medidas:
- **Figma (px) → React Native (dp):** Geralmente 1:1
- **Verificar densidade:** Teste em diferentes dispositivos

## 📱 Testando as Customizações

### 1. Teste em Diferentes Dispositivos
```bash
# iOS
npx react-native run-ios --simulator="iPhone 14"
npx react-native run-ios --simulator="iPhone SE (3rd generation)"

# Android
npx react-native run-android
```

### 2. Teste com Diferentes Tamanhos de Fonte
- Vá em Configurações > Acessibilidade > Tamanho da Fonte
- Teste com tamanhos grandes e pequenos

### 3. Teste de Contraste
- Use ferramentas como WebAIM Contrast Checker
- Garanta pelo menos 4.5:1 para texto normal

## 🔧 Dicas Avançadas

### 1. Usando Styled Components (Opcional)
```bash
npm install styled-components
npm install --save-dev @types/styled-components-react-native
```

### 2. Animações Suaves
```typescript
import { Animated } from 'react-native';

// Adicione animações conforme o design
const fadeAnim = new Animated.Value(0);

Animated.timing(fadeAnim, {
  toValue: 1,
  duration: 300,
  useNativeDriver: true,
}).start();
```

### 3. Responsividade
```typescript
import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: width * 0.05, // 5% da largura
  },
});
```

## 📋 Checklist Final

- [ ] Todas as cores correspondem ao Figma
- [ ] Tipografia está correta
- [ ] Espaçamentos estão precisos
- [ ] Componentes têm o visual correto
- [ ] App funciona em iOS e Android
- [ ] Testado em diferentes tamanhos de tela
- [ ] Acessibilidade verificada
- [ ] Performance está boa

## 🆘 Problemas Comuns

### Fonte não aparece
- Verifique se os arquivos estão nas pastas corretas
- Reinicie o Metro bundler
- Limpe o cache: `npx react-native start --reset-cache`

### Cores não aplicam
- Verifique se importou corretamente
- Confirme se não há cache de estilo

### Layout quebrado
- Verifique flexbox
- Teste em diferentes dispositivos
- Use Flipper para debug

---

**Boa customização! 🚀**
