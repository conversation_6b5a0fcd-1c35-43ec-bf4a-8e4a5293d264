#!/bin/bash

# Script para executar o projeto TEGRA React Native

echo "🚀 TEGRA - Ficha Cadastral Digital"
echo "=================================="
echo ""

# Verificar se o Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Node.js não está instalado. Por favor, instale o Node.js primeiro."
    exit 1
fi

# Verificar se o npm está instalado
if ! command -v npm &> /dev/null; then
    echo "❌ npm não está instalado. Por favor, instale o npm primeiro."
    exit 1
fi

echo "✅ Node.js e npm encontrados"
echo ""

# Verificar se as dependências estão instaladas
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    npm install
    echo "✅ Dependências instaladas"
    echo ""
fi

# Verificar se é macOS e instalar pods para iOS
if [[ "$OSTYPE" == "darwin"* ]]; then
    if [ ! -d "ios/Pods" ]; then
        echo "🍎 Instalando CocoaPods para iOS..."
        cd ios && pod install && cd ..
        echo "✅ CocoaPods instalados"
        echo ""
    fi
fi

echo "Escolha uma opção:"
echo "1) Iniciar Metro Bundler"
echo "2) Executar no Android"
echo "3) Executar no iOS (apenas macOS)"
echo "4) Executar Metro + Android"
echo "5) Executar Metro + iOS (apenas macOS)"
echo ""

read -p "Digite sua escolha (1-5): " choice

case $choice in
    1)
        echo "🚀 Iniciando Metro Bundler..."
        npx react-native start
        ;;
    2)
        echo "🤖 Executando no Android..."
        npx react-native run-android
        ;;
    3)
        if [[ "$OSTYPE" == "darwin"* ]]; then
            echo "🍎 Executando no iOS..."
            npx react-native run-ios
        else
            echo "❌ iOS só pode ser executado no macOS"
        fi
        ;;
    4)
        echo "🚀 Iniciando Metro Bundler em background..."
        npx react-native start &
        sleep 5
        echo "🤖 Executando no Android..."
        npx react-native run-android
        ;;
    5)
        if [[ "$OSTYPE" == "darwin"* ]]; then
            echo "🚀 Iniciando Metro Bundler em background..."
            npx react-native start &
            sleep 5
            echo "🍎 Executando no iOS..."
            npx react-native run-ios
        else
            echo "❌ iOS só pode ser executado no macOS"
        fi
        ;;
    *)
        echo "❌ Opção inválida"
        exit 1
        ;;
esac
