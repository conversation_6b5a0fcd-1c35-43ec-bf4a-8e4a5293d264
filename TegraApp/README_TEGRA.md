# TEGRA - Ficha Cadastral Digital

Aplicativo React Native para gerenciamento de fichas cadastrais digitais.

## 🚀 Funcionalidades

- ✅ Tela de Login moderna e responsiva
- ✅ Validação de email em tempo real
- ✅ Navegação entre telas (Login/Home)
- ✅ Interface limpa e profissional
- ✅ Suporte a iOS e Android
- ✅ TypeScript para maior segurança de tipos
- ✅ Estrutura de pastas organizada

## 📱 Telas Implementadas

### Tela de Login
- Campo de email com validação
- Campo de senha com opção de mostrar/ocultar
- Botão de login com estado de carregamento
- Link para recuperação de senha
- Link para cadastro
- Design responsivo e acessível

### Tela Home
- Boas-vindas personalizadas
- Cards informativos
- Botão de logout
- Interface limpa e moderna

## 🛠️ Tecnologias Utilizadas

- **React Native 0.81.1** - Framework principal
- **TypeScript** - Tipagem estática
- **React Native Safe Area Context** - Gerenciamento de área segura
- **Metro** - Bundler do React Native

## 📁 Estrutura do Projeto

```
TegraApp/
├── src/
│   ├── screens/
│   │   ├── LoginScreen.tsx
│   │   └── HomeScreen.tsx
│   ├── styles/
│   │   ├── colors.ts
│   │   └── globalStyles.ts
│   ├── types/
│   │   └── index.ts
│   └── components/
├── App.tsx
├── package.json
└── README_TEGRA.md
```

## 🚀 Como Executar

### Pré-requisitos

1. **Node.js** (versão 16 ou superior)
2. **npm** ou **yarn**
3. **React Native CLI**
4. **Android Studio** (para Android)
5. **Xcode** (para iOS - apenas macOS)

### Instalação

1. **Clone o repositório e navegue para a pasta do projeto:**
   ```bash
   cd TegraApp
   ```

2. **Instale as dependências:**
   ```bash
   npm install
   # ou
   yarn install
   ```

3. **Para iOS (apenas macOS):**
   ```bash
   cd ios && pod install && cd ..
   ```

### Executando o Projeto

1. **Inicie o Metro Bundler:**
   ```bash
   npx react-native start
   # ou se a porta 8081 estiver ocupada:
   npx react-native start --port 8082
   ```

2. **Em outro terminal, execute o app:**

   **Para Android:**
   ```bash
   npx react-native run-android
   ```

   **Para iOS:**
   ```bash
   npx react-native run-ios
   ```

## 🎨 Personalização

### Cores
As cores do app estão definidas em `src/styles/colors.ts`. Você pode personalizar:
- Cores primárias (azul TEGRA)
- Cores de status (sucesso, erro, aviso)
- Cores neutras (cinzas)

### Estilos Globais
Os estilos reutilizáveis estão em `src/styles/globalStyles.ts`.

## 🔧 Configurações Adicionais

### Adaptação ao Design do Figma

Para adaptar a tela de login ao design específico do Figma:

1. **Atualize as cores** em `src/styles/colors.ts`
2. **Modifique os estilos** em `src/screens/LoginScreen.tsx`
3. **Adicione assets** (logos, ícones) na pasta `src/assets/`
4. **Ajuste tipografia** conforme o design

### Exemplo de customização:

```typescript
// src/styles/colors.ts
export const Colors = {
  primary: '#SUA_COR_PRIMARIA',
  // ... outras cores
};
```

## 📝 Próximos Passos

- [ ] Integração com API de autenticação
- [ ] Implementação de navegação com React Navigation
- [ ] Telas de cadastro e recuperação de senha
- [ ] Persistência de dados com AsyncStorage
- [ ] Implementação de formulários dinâmicos
- [ ] Testes unitários e de integração

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Suporte

Para dúvidas ou suporte, entre em contato através dos issues do GitHub.

---

**TEGRA - Ficha Cadastral Digital** 🚀
