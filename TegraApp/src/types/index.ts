// Tipos para autenticação
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Tipos para navegação
export interface NavigationProps {
  onLogin?: (email: string, password: string) => void;
  onLogout?: () => void;
  userEmail?: string;
}

// Tipos para formulários
export interface FormField {
  value: string;
  error?: string;
  isValid: boolean;
}

export interface LoginForm {
  email: FormField;
  password: FormField;
}

// Tipos para API
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// Tipos para componentes
export interface ButtonProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'primary' | 'secondary' | 'outline';
}

export interface InputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  error?: string;
  disabled?: boolean;
}
