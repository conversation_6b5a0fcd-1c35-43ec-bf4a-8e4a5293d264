// Configuração do tema TEGRA
export const theme = {
  // Configurações de marca
  brand: {
    name: 'TEGRA',
    subtitle: 'Ficha Cadastral Digital',
    primaryColor: '#1E40AF', // Azul TEGRA
  },

  // Configurações de layout
  layout: {
    borderRadius: {
      small: 8,
      medium: 12,
      large: 16,
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
    },
    shadows: {
      small: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 1,
      },
      medium: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      },
      large: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 6,
      },
    },
  },

  // Configurações de tipografia
  typography: {
    fontSizes: {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
      xxl: 24,
      xxxl: 28,
      title: 32,
    },
    fontWeights: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
    lineHeights: {
      tight: 1.2,
      normal: 1.5,
      relaxed: 1.8,
    },
  },

  // Configurações de componentes
  components: {
    button: {
      height: {
        small: 40,
        medium: 52,
        large: 60,
      },
      borderRadius: 12,
    },
    input: {
      height: 52,
      borderRadius: 12,
    },
    card: {
      borderRadius: 12,
      padding: 20,
    },
  },

  // Configurações de animação
  animations: {
    duration: {
      fast: 150,
      normal: 300,
      slow: 500,
    },
    easing: {
      easeInOut: 'ease-in-out',
      easeOut: 'ease-out',
      easeIn: 'ease-in',
    },
  },
};

export type Theme = typeof theme;
