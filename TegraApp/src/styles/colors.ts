// Paleta de cores do TEGRA
export const colors = {
  // Cores primárias
  primary: {
    50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    600: '#1E40AF', // Cor principal do TEGRA
    700: '#1E3A8A',
    800: '#1E3A8A',
    900: '#1E3A8A',
  },

  // Cores neutras
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },

  // Cores de status
  success: {
    50: '#F0FDF4',
    100: '#DCFCE7',
    200: '#BBF7D0',
    300: '#86EFAC',
    400: '#4ADE80',
    500: '#22C55E',
    600: '#16A34A',
    700: '#15803D',
    800: '#166534',
    900: '#14532D',
  },

  error: {
    50: '#FEF2F2',
    100: '#FEE2E2',
    200: '#FECACA',
    300: '#FCA5A5',
    400: '#F87171',
    500: '#EF4444',
    600: '#DC2626',
    700: '#B91C1C',
    800: '#991B1B',
    900: '#7F1D1D',
  },

  warning: {
    50: '#FFFBEB',
    100: '#FEF3C7',
    200: '#FDE68A',
    300: '#FCD34D',
    400: '#FBBF24',
    500: '#F59E0B',
    600: '#D97706',
    700: '#B45309',
    800: '#92400E',
    900: '#78350F',
  },

  info: {
    50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    600: '#2563EB',
    700: '#1D4ED8',
    800: '#1E40AF',
    900: '#1E3A8A',
  },

  // Cores específicas do app
  background: '#FFFFFF',
  surface: '#F9FAFB',
  text: {
    primary: '#1F2937',
    secondary: '#6B7280',
    disabled: '#9CA3AF',
    inverse: '#FFFFFF',
  },
  border: '#E5E7EB',
  shadow: 'rgba(0, 0, 0, 0.1)',
};

// Aliases para facilitar o uso
export const Colors = {
  // Principais
  primary: colors.primary[600],
  primaryLight: colors.primary[100],
  primaryDark: colors.primary[700],

  // Backgrounds
  background: colors.background,
  surface: colors.surface,
  card: colors.background,

  // Textos
  text: colors.text.primary,
  textSecondary: colors.text.secondary,
  textDisabled: colors.text.disabled,
  textInverse: colors.text.inverse,

  // Bordas e divisores
  border: colors.border,
  divider: colors.gray[200],

  // Estados
  success: colors.success[500],
  error: colors.error[500],
  warning: colors.warning[500],
  info: colors.info[500],

  // Inputs
  inputBackground: colors.gray[50],
  inputBorder: colors.gray[300],
  inputBorderFocus: colors.primary[600],
  inputBorderError: colors.error[500],

  // Botões
  buttonPrimary: colors.primary[600],
  buttonPrimaryDisabled: colors.gray[400],
  buttonSecondary: colors.gray[100],
  buttonOutline: 'transparent',

  // Sombras
  shadow: colors.shadow,
};
