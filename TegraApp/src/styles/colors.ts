// Paleta de cores do TEGRA - Baseada no design do Figma
export const colors = {
  // Cores da marca TEGRA
  tegra: {
    gold: '#D4AF37',        // Cor dourada do logo TEGRA
    black: '#000000',       // Cor do botão Entrar
    darkText: '#333333',    // Cor do texto principal
    grayText: '#666666',    // Cor do texto secundário
    lightGray: '#999999',   // Cor do placeholder
    border: '#E0E0E0',      // Cor das bordas dos inputs
    background: '#FFFFFF',  // Fundo branco
    disabled: '#CCCCCC',    // Cor do botão desabilitado
  },

  // Cores primárias (mantidas para compatibilidade)
  primary: {
    50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    600: '#D4AF37', // Atualizada para a cor dourada do TEGRA
    700: '#B8941F',
    800: '#9C7E1A',
    900: '#806815',
  },

  // Cores neutras
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },

  // Cores de status
  success: {
    50: '#F0FDF4',
    100: '#DCFCE7',
    200: '#BBF7D0',
    300: '#86EFAC',
    400: '#4ADE80',
    500: '#22C55E',
    600: '#16A34A',
    700: '#15803D',
    800: '#166534',
    900: '#14532D',
  },

  error: {
    50: '#FEF2F2',
    100: '#FEE2E2',
    200: '#FECACA',
    300: '#FCA5A5',
    400: '#F87171',
    500: '#EF4444',
    600: '#DC2626',
    700: '#B91C1C',
    800: '#991B1B',
    900: '#7F1D1D',
  },

  warning: {
    50: '#FFFBEB',
    100: '#FEF3C7',
    200: '#FDE68A',
    300: '#FCD34D',
    400: '#FBBF24',
    500: '#F59E0B',
    600: '#D97706',
    700: '#B45309',
    800: '#92400E',
    900: '#78350F',
  },

  info: {
    50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    600: '#2563EB',
    700: '#1D4ED8',
    800: '#1E40AF',
    900: '#1E3A8A',
  },

  // Cores específicas do app
  background: '#FFFFFF',
  surface: '#F9FAFB',
  text: {
    primary: '#1F2937',
    secondary: '#6B7280',
    disabled: '#9CA3AF',
    inverse: '#FFFFFF',
  },
  border: '#E5E7EB',
  shadow: 'rgba(0, 0, 0, 0.1)',
};

// Aliases para facilitar o uso - Cores TEGRA
export const Colors = {
  // Cores principais TEGRA
  tegraGold: colors.tegra.gold,
  tegraBlack: colors.tegra.black,
  tegraText: colors.tegra.darkText,
  tegraTextSecondary: colors.tegra.grayText,
  tegraPlaceholder: colors.tegra.lightGray,
  tegraBorder: colors.tegra.border,
  tegraBackground: colors.tegra.background,
  tegraDisabled: colors.tegra.disabled,

  // Principais (compatibilidade)
  primary: colors.tegra.gold,
  primaryLight: colors.primary[100],
  primaryDark: colors.primary[700],

  // Backgrounds
  background: colors.tegra.background,
  surface: colors.surface,
  card: colors.tegra.background,

  // Textos
  text: colors.tegra.darkText,
  textSecondary: colors.tegra.grayText,
  textDisabled: colors.text.disabled,
  textInverse: colors.text.inverse,

  // Bordas e divisores
  border: colors.tegra.border,
  divider: colors.gray[200],

  // Estados
  success: colors.success[500],
  error: colors.error[500],
  warning: colors.warning[500],
  info: colors.info[500],

  // Inputs
  inputBackground: colors.tegra.background,
  inputBorder: colors.tegra.border,
  inputBorderFocus: colors.tegra.gold,
  inputBorderError: colors.error[500],

  // Botões
  buttonPrimary: colors.tegra.black,
  buttonPrimaryDisabled: colors.tegra.disabled,
  buttonSecondary: colors.gray[100],
  buttonOutline: 'transparent',

  // Sombras
  shadow: colors.shadow,
};
